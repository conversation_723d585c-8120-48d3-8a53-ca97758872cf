// English language file
export default {
  dir: "ltr",
  settings: {
    title: "Settings",
    save: "Save",
    close: "Close",
    language: "Language",
  },
  info: {
    title: "Info",
    pwaInstall: {
      title: "Install as App",
      description:
        "Install scTimer as a Progressive Web App for the best experience. Works offline and feels like a native app.",
      install: "Install App",
      iosTitle: "iOS/iPad Installation:",
      iosStep1: "1. Tap the Share button",
      iosStep2: '2. Scroll down and tap "Add to Home Screen"',
      iosStep3: '3. Tap "Add" to install',
      note: "Available on Chrome, Safari, and other modern browsers",
    },
    shortcuts: {
      title: "Keyboard Shortcuts",
      timer: "Timer Controls",
      spacebar: "Start/stop timer",
      escape: "Cancel inspection & close modals",
      navigation: "Navigation & Actions",
      generate: "Generate new scramble",
      list: "Toggle solve times list",
      settings: "Open settings",
      edit: "Edit current scramble",
      copy: "Copy scramble to clipboard",
      stats: "Open detailed statistics",
      display: "Display Toggles",
      visualization: "Toggle puzzle visualization",
      statistics: "Toggle statistics display",
      darkMode: "Toggle dark mode",
      inspection: "Toggle WCA inspection",
      penalties: "Penalty Management",
      removePenalty: "Remove penalty from recent solve",
      addPlus2: "Add +2 penalty to recent solve",
      addDNF: "Add DNF penalty to recent solve",
      session: "Session Management",
      emptySession: "Empty current session",
      exportSession: "Export current session",
      session1: "Switch to session 1",
      session2: "Switch to session 2",
      session3: "Switch to session 3",
      newSession: "Create new session",
    },
    gestures: {
      title: "Mobile Gestures",
      swipeDown: "Swipe Down",
      swipeDownDesc: "Delete recent solve",
      swipeUp: "Swipe Up",
      swipeUpDesc: "Cycle through penalties (none/+2/DNF)",
      swipeLeft: "Swipe Left",
      swipeLeftDesc: "LTR: New scramble | RTL: Times list",
      swipeRight: "Swipe Right",
      swipeRightDesc: "LTR: Times list | RTL: New scramble",
      doubleClick: "Double Click",
      doubleClickDesc: "Copy current scramble (PC/Mobile)",
      longPress: "Long Press/Click & Hold",
      longPressDesc: "Edit current scramble (PC/Mobile)",
    },
    features: {
      title: "Key Features",
      timer: "Professional Timer",
      timerDesc: "WCA-compliant timing with inspection mode",
      puzzles: "All WCA Events",
      puzzlesDesc: "Complete support for all official WCA puzzle events",
      statistics: "Advanced Statistics",
      statisticsDesc: "Detailed analytics with ao5, ao12, ao100",
      scrambles: "Official Scrambles",
      scramblesDesc: "WCA-standard scramble generation with 2D visualization",
      multilingual: "Multilingual Support",
      multilingualDesc: "15+ languages with RTL support",
      sync: "Google Drive Sync",
      syncDesc: "Cross-device synchronization with smart merging",
    },
    sync: {
      title: "Google Drive Sync",
      description:
        "Synchronize your solve times across all devices using Google Drive. Your data is securely stored in your personal Google Drive account.",
      secure: "Secure & Private",
      automatic: "Automatic Sync",
      offline: "Offline Support",
      smartMerge: "Smart Merging",
      note: "Enable Google Drive sync in Settings to keep your times synchronized across all your devices.",
    },
  },
  timerOptions: {
    title: "Timer Options",
    useInspection: "Use WCA Inspection (15s)",
    inspectionSound: "Inspection Sound:",
    inspectionSoundNone: "None",
    inspectionSoundVoice: "Voice",
    inspectionSoundBeep: "Beep",
    stackmatResetInspection: "Stackmat Reset Triggers Inspection",
    stackmatResetNote: "Note: Only works when timer is not 0.000",
    inputTimer: "Typing Timer Mode (Enter times manually)",
    timerMode: "Timer Mode:",
    timerModeTimer: "Timer",
    timerModeTyping: "Typing",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Coming Soon)",
    microphoneInput: "Microphone Input",
    microphoneAuto: "Auto-detect",
    microphoneNote: "Choose your Y splitter or external microphone",
    decimalPlaces: "Decimal Places:",
    decimalPlacesNone: "None (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Display Options",
    showVisualization: "Show Puzzle Visualization",
    showStats: "Show Statistics",
    showDebug: "Show Debug Information",
    darkMode: "Dark Mode",
    showFMCKeyboard: "Show FMC Keyboard",
    scrambleFontSize: "Scramble Font Size",
  },
  app: {
    title: "scTimer",
    description: "A speedcubing timer with WCA inspection and statistics",
    enterTime: "Enter time",
    enterSolveTime: "Enter solve time manually",
    generateScrambles: "Generate Scrambles",
    outOf: "Out of:",
    numberOfCubes: "Number of cubes (minimum 2):",
    numberOfCubesSolved: "Number of cubes solved:",
  },
  timer: {
    ready: "Ready",
    running: "Running",
    idle: "Idle",
    inspection: "Inspection",
    holding: "Holding",
  },
  stats: {
    title: "Statistics",
    best: "Best",
    worst: "Worst",
    mean: "Mean",
    avg5: "ao5",
    avg12: "ao12",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Solves",
    attempts: "Attempts",
  },
  statsDetails: {
    title: "Statistics Details",
    titleFor: "Statistics Details for",
    overview: "Overview",
    averages: "Averages",
    records: "Records",
    timeDistribution: "Time Distribution",
    progressChart: "Progress Over Time",
    sessionAnalysis: "Session Analysis",
    predictions: "Predictions",
    standardDeviation: "Std Dev",
    bestSingle: "Best Single",
    bestAo5: "Best ao5",
    bestAo12: "Best ao12",
    bestAo100: "Best ao100",
    bestAo1000: "Best ao1000",
    totalTime: "Total Time",
    averageTime: "Average Time",
    solvesPerHour: "Solves/Hour",
    consistency: "Consistency",
    nextAo5: "Next ao5 Target",
    nextAo12: "Next ao12 Target",
    improvementRate: "Improvement Rate",
    targetTime: "Target Time",
    currentSession: "Current Session",
    allSessions: "All Sessions",
    importTimes: "Import Times",
    exportJSON: "Export JSON",
    exportCSV: "Export CSV",
  },
  solveDetails: {
    title: "Solve Details",
    time: "Time",
    date: "Date",
    scramble: "Scramble",
    editedScramble: "Edited Scramble",
    copyScramble: "Copy scramble",
    penalty: "Penalty",
    none: "None",
    comment: "Comment",
    addComment: "Add a comment...",
    save: "Save",
    share: "Share",
    plusTwo: "+2",
    dnf: "DNF",
  },

  gestures: {
    scrambleCopied: "Scramble copied",
    noSolvesToDelete: "No solves to delete",
    solveDeleted: "Solve deleted",
    cannotAddPenaltyMBLD: "Cannot add penalty to MBLD solve",
    dnfRemoved: "DNF removed",
    dnfAdded: "DNF added",
    plus2Added: "+2 penalty added",
    penaltyRemoved: "Penalty removed",
    newScrambleGenerated: "New scramble generated",
    timesPanelOpened: "Times panel opened",
  },
  times: {
    title: "Solve Times",
    clear: "Clear Times",
    close: "Close",
    delete: "Delete time",
    confirmClear: "Are you sure you want to clear all times for this event?",
    confirmDelete: "Are you sure you want to delete this time?",
  },
  buttons: {
    viewTimes: "View Times",
    ok: "OK",
    cancel: "Cancel",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Blindfolded",
    "333fm": "3×3×3 Fewest Moves",
    "333oh": "3×3×3 One-Handed",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Blindfolded",
    "555bf": "5×5×5 Blindfolded",
    "333mbf": "3×3×3 Multi-Blind",
  },
  mbld: {
    cubeCount: "Cubes",
    solvedCount: "Solved Cubes",
    totalCount: "Total Cubes",
    totalCubes: "Total Cubes",
    cubesSolved: "Cubes Solved",
    bestPoints: "Best Points",
    successRate: "Success Rate",
    points: "points",
    save: "Save Result",
    visualizations: "Multi-Blind Visualizations",
    scrambles: "Multi-Blind Scrambles",
    enterValidNumber: "Please enter a valid number of solved cubes.",
    noScrambles:
      "No MBLD scrambles available. Please select the 3×3×3 Multi-Blind event first.",
    visualizationNotFound:
      "Visualization modal not found. Please refresh the page and try again.",
    containerNotFound:
      "Visualization container not found. Please refresh the page and try again.",
    clickToView: "Click to view all cube visualizations & scrambles",
    clickToViewScrambles: "Click to view all scrambles",
    clickToViewScramblesCount: "Click to view all {0} scrambles",
    setup: "Multi-Blind Setup",
    results: "Multi-Blind Results",
    generateScrambles: "Generate Scrambles",
    saveResult: "Save Result",
    cubeNumber: "Cube",
    numberOfCubesMinimum: "Number of cubes (minimum 2):",
    numberOfCubesSolved: "Number of cubes solved:",
    saveFirst: "Please save your result first.",
    visualizationsTitle: "Multi-Blind Visualizations ({0} cubes)",
    timeLimit: "Time limit: {0} minutes",
    timeLimitExceeded: "Time limit exceeded. Result will be DNF.",
    negativePoints: "Negative points. Result will be DNF.",
  },
  modals: {
    error: "Error",
    warning: "Warning",
    info: "Information",
    confirm: "Confirm",
    prompt: "Input Required",
  },
  stackmat: {
    error: "Stackmat Error",
    noMicrophone:
      "Failed to start Stackmat timer: No microphone found. Please connect a microphone and try again.",
    connected: "Connected",
    disconnected: "Disconnected",
    settingUp: "Setting up...",
  },
  sessions: {
    newSessionTitle: "New Session",
    editSessionTitle: "Edit Session",
    sessionName: "Session Name:",
    sessionNamePlaceholder: "My Session",
    puzzleType: "Puzzle Type:",
    create: "Create",
    save: "Save",
  },
  scramble: {
    loading: "Loading scramble...",
  },
  debug: {
    timerState: "Timer State: ",
    spaceHeldFor: "Space Held For: ",
    currentEvent: "Current Event: ",
    scrambleSource: "Scramble Source: ",
  },
  fmc: {
    title: "Fewest Moves Challenge",
    info: "Solve the cube in the fewest possible moves. You have 60 minutes to find a solution.",
    timeRemaining: "Time:",
    scramble: "Scramble:",
    solution: "Solution:",
    moveCount: "Moves:",
    moves: "moves",
    submit: "Submit",
    resultTitle: "FMC Result",
    resultTime: "Time:",
    resultSolution: "Solution:",
    resultOk: "OK",
    solutionPlaceholder:
      "Enter your solution here using standard WCA notation...",
    notationHelp: "Notation Help:",
    notationHelpContent:
      "Face turns: U, D, L, R, F, B (with ' or 2 suffixes)<br>Wide moves: Uw, Dw, etc.<br>Slice moves: M, E, S<br>Rotations: x, y, z (not counted in move total)",
    submitSolution: "Submit Solution",
    validSolution: "Valid solution",
    invalidNotation: "Invalid notation detected",
    tooManyMoves: "Solution exceeds 80 moves limit",
    timeExceeded:
      "Time limit exceeded. Your solution will be marked as DNF if not submitted.",
    confirmClose:
      "Are you sure you want to close? Your attempt will be marked as DNF.",
    dnfReasonTimeout: "Time limit exceeded",
    dnfReasonInvalid: "Invalid notation",
    dnfReasonTooManyMoves: "Solution exceeds 80 moves",
    dnfReasonAbandoned: "Attempt abandoned",
    confirmSubmit: "Are you sure you want to submit your solution?",
    pressToStart: "Press space to start FMC attempt",
    solutionAccepted: "Solution accepted",
    clickToViewTwizzle: "Click the link below to view the solution in Twizzle",
    viewOnTwizzle: "View on Twizzle",
    moveCountLabel: "Move count:",
    movesHTM: "moves (HTM)",
    timeUsedLabel: "Time used:",
    loadingFMC: "loading FMC",
    generatingScramble: "generating scramble and preparing interface",
  },
  tutorial: {
    // Welcome Modal
    welcomeTitle: "Welcome to scTimer!",
    welcomeSubtitle: "Your professional speed cubing timer",
    selectLanguage: "Select Language:",
    feature1: "WCA Standard Timer",
    feature2: "Advanced Statistics",
    feature3: "All WCA Events",
    feature4: "Scramble Generator",
    welcomeDescription:
      "Would you like a quick tour to learn how to use scTimer effectively? The tutorial will guide you through the main features in just a few steps.",
    skipTutorial: "Skip Tutorial",
    startTour: "Start Tour",

    // Tutorial Steps
    step1: {
      title: "Scramble Display",
      text: "This shows the scramble sequence for your current puzzle. Each scramble is randomly generated following WCA standards.",
    },
    step2: {
      title: "Timer Controls",
      text: "Press and hold SPACEBAR to start timing, release to begin solving. On mobile, tap and hold the timer area. The timer follows WCA inspection standards.",
    },
    step3: {
      title: "Event Selector",
      text: "Choose from all WCA events including 3x3x3, 2x2x2, 4x4x4, and many more puzzle types. Click or tap to open the dropdown menu.",
    },
    step4: {
      title: "Statistics Tracking",
      text: "Track your progress with detailed statistics including best time, averages of 5, 12, and 100 solves. Click on any stat to see more details.",
    },
    step5: {
      title: "Generate New Scramble",
      text: "Generate a new scramble when you're ready for your next solve. Keyboard shortcut: Press N or click the shuffle icon.",
    },
    step6: {
      title: "Settings & Customization",
      text: "Customize your timer experience with inspection time, sound options, timer modes, and display preferences. Keyboard shortcut: Press S.",
    },
    step7: {
      title: "Keyboard Shortcuts",
      text: "Master these shortcuts: SPACEBAR (start/stop timer), N (new scramble), S (settings), ESC (close modals), Arrow keys (navigate). On mobile, use swipe gestures!",
    },
    step8: {
      title: "Mobile Gestures",
      text: "On mobile devices: Swipe left to open times panel, swipe right to close it, tap and hold timer to start, double-tap scramble to copy it. Pinch to zoom on visualizations.",
    },
    step9: {
      title: "Pro Tips & Features",
      text: "Enable inspection time in settings for WCA practice. Use different sessions to track various events. Export your times for analysis. The timer works offline as a PWA!",
    },

    // Navigation
    previous: "Previous",
    next: "Next",
    finish: "Finish",
    close: "Close",
    stepCounter: "of",

    // Settings
    restartTutorial: "Restart Tutorial",
  },
};
